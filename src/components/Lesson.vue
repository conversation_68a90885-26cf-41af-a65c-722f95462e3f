<template>
  <div>
    <img :src="lesson.preview" alt="" />
    <h3 @dblclick="inputShow = true">
      <input
        v-if="inputShow"
        type="text"
        :value="lesson.title"
        @input="changeTitle"
        @keyup.enter="inputShow = false"
        @blur="inputShow = false" />
      <strong v-else>
        {{ lesson.title }}
      </strong>
    </h3>
       <h3 @dblclick="inputPriceShow = true">
      <input
        v-if="inputPriceShow"
        type="text"
        :value="lesson.price"
        @input="$emit('update:price',$event.target.value)"
        @keyup.enter="inputPriceShow = false"
        @blur="inputPriceShow = false" />
      <strong v-else>
        {{ lesson.price }}
      </strong>
    </h3>
    <span @click="del">x</span>
  </div>
</template>

<script>
  export default {
    name: "MyComponent",
    props: ["lesson","modelValue","price",'modelModifiers'],
    emits: {
        'update:modelValue':null,
        'update:price':null,
      del(v) {
        if (/^\d+$/.test(v)) {
          return true;
        }
        console.error("删除失败");
        throw new Error("del 需要参数");
      },
    },
    created(){
        console.log(this.modelModifiers)
    },
    data() {
      return {
        inputShow: false,
        inputPriceShow:false
      };
    },
    methods: {
      changeTitle($event){
        let val = $event.target.value
        if(this.modelModifiers.toupper){
            val = val.toUpperCase()
        }
        const substr=Object.keys(this.modelModifiers).find(m=>/^substr_/.test(m));
        if(substr){
          let info=substr.split('_')
          val=val.substring(0,info[1])
        }
        this.$emit('update:modelValue',val)
      },
      del() {
        if (!confirm("确定删除吗？")) {
          return;
        }
        this.$emit("del", this.lesson.id);
      },
    },
  };
</script>

<style scoped lang="scss">
  div {
    border: 1px solid #ddd;
    text-align: center;
    transition: 1s;
    position: relative;
    &:hover {
      box-shadow: 0 0 20px #aaa;
      > span {
        opacity: 1;
      }
    }
    span {
      display: block;
      background-color: #666;
      color: #fff;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 10px;
      right: 10px;
      cursor: pointer;
      font-size: 12px;
      opacity: 0;
      transition: 1s;
    }
    h3 {
      padding: 0 0 20px 0;
      margin: 0;
      font-size: 16px;
    }
    img {
      width: 100%;
      height: 250px;
    }
  }
</style>
