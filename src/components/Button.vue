<template>
  <section>
      <!-- <div :class="[type, { disabled }]" @click="changeText" :id="$attrs.id"> -->
      <div :class="[type, { disabled }]"  :id="$attrs.id"  >{{text}}
      <!-- <span v-if="MsTip">{{MsTip}}</span> -->
      <!-- {{ text }} -->
      <hr />
    </div>
 </section>
 <h2 v-bind="$attrs">click</h2>

</template>

<script>
  export default {
    inheritAttrs: false,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    props: {
        click:{
            type:Function,
        },
      content: {
        type: String,
        // type:Object,
        required: true,
        default:'确定'
      },
      arr: {
        type: Array,
        default() {
          return ["1,2", 3];
        },
      },
      type: {
        type: String,
        default: "info",
        validator(v) {
          return ["info", "success", "danger"].includes(v);
        },
      },
      MsTip: String,
      disabled: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        text: this.content,
      };
    },
    methods: {
      changeText() {
        (this.text = "loading..."),
          setTimeout(() => {
            this.text = this.content;
          }, 3000);
      },
    },
    watch: {
      content(v) {
        this.text = v;
      },
    },
  };
</script>

<style scoped lang="scss">
  div {
    color: white;
    padding: 5px 10px;
    border-radius: 10px;
    opacity: 0.6;
    transition: 1s;
    display: inline-block;
    cursor: pointer;
    &:hover {
      opacity: 1;
    }
    &.info {
      background-color: #ddd;
    }
    &.success {
      background-color: #5cb85c;
    }
    &.danger {
      background-color: #d9534f;
    }
    &.disabled {
      opacity: 1;
      background-color: #ccc !important;
      color: #666;
      cursor: default;
    }
  }
</style>
