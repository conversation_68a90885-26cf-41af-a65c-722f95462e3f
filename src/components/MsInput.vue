<template>
  <div>
    <input type="text" :value="content" @input="changeInput"/>
    {{ content }}
</div>
</template>

<script>
export default {
  name: 'MsInput',
  props:['modelValue'],
  emit:['update:modelValue'],
  data() {
    return {
        content:this.modelValue
    };
  },
  methods:{
    changeInput(e){
        this.content=e.target.value
        this.$emit('update:modelValue',this.content)
    }
  }
};
</script>

<style scoped>
/* 样式 */
</style>