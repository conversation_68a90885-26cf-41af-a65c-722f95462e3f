<script>
export default {
    inject:['config'],
  components: {

  },
  props: {

  },
  data() {
    return {

    }
  },
}
</script>
<template>
    <card>
        <template #header>站点信息</template>
        <div>
            <Xinput title="网站标题" v-model="config.site.title"/>
            <Xinput title="网站邮箱" v-model="config.site.mail"/>
            <Xinput title="微信公众号" v-model="config.site.wx"/>
            <XTextarea title="站点介绍" v-model="config.site.desc"/>
        </div>
    </card>

</template>

<style lang="scss" scoped>

</style>
    