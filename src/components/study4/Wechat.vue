<template>

  <Card>
    <template #header> 微信 </template>
    <div>
      <Xinput title="微信号" v-model="config.Wechat.appid"/>
      <Xinput title="AppSecret" v-model="config.Wechat.secret"/>
      <XTextarea title="Token" v-model="config.Wechat.token" :rows="3"/>
      <Xinput title="API" v-model="url"/>
    </div>
  </Card>
</template>

<script>
  export default {
    inject:['config'],
    data() {
      return {
        url:'https://api.weixin.qq.com/'
      };
    },
    methods:{
      show(){
        alert(this.url)
      }
    }
  };
</script>

<style scoped>
  /* 样式 */
</style>
