<template>
  <main>
    <div
      v-for="(component, index) of components"
      :key="index"
      :class="{active:component.name==currentComponent}"
      @click="currentComponent = component.name">
      {{ component.title }}
    </div>
  </main>
  <button @click="callComponent">QUEREN</button>
  <keep-alive>
  <component :is="currentComponent"  ref="componentS" />
  </keep-alive>
</template>

<script>
  import Wechat from "../../components/study4/Wechat.vue";
  import pay from "../../components/study4/Pay.vue";
  import Site from "../../components/study4/Site.vue";
import { computed } from "vue";
import config from "../../components/study4/config";
  export default {
    name: "study4app",
    components: {
      Wechat,
      pay,
      Site,
    },
    provide(){
      return {
        webName:computed(()=>this.teacher),
        config:this.config
      }
    },
    methods:{
      callComponent(){
        this.$refs.componentS.show()
      }
    },
    data() {
      return {
        config,
        teacher:'迷失',
        currentComponent: "wechat",
        components: [
          {title: "微信管理", name: "wechat"},
          {title: "在线支付", name: "pay"},
          {title: "网站设置", name: "site"},
        ],
      };
    },
  };
</script>

<style lang="scss" scoped>
  main {
    display: flex;
    div {
      border: 1px solid #ddd;
      padding: 10px;
      margin-right: 20px;
      cursor: pointer;
      transition: 0.5s;
      &.active {
        background-color: #16a085;
        color: #fff;
      }
    }
  }
</style>
