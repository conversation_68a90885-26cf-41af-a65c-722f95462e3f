<template>
  <div>
    <!-- <MsButton :content="btContent" type="success" />
    <hr>
    <MsButton content="确定" type="success" />
    <hr>
    <MsButton content="删除" type="danger" />
    <hr>
    <MsButton content="禁用" :disabled="true" />
    <hr>
    <MsButton v-bind="{content:'保存',type:'success',style:'margin-top:20px;'}"/>
    <hr>
    <MsButton content="提交" type="success" MsTip="提交后不可回滚:" />
    <hr> -->
    <MsButton :content="btContent" type="success" id="btn" @click="show"/>
    <!-- <hr>
    <button @click="btContent='完成'">父组件</button>
    {{btContent}} -->
</div>
</template> 

<script>
import MsButton from '../components/Button.vue'
export default {
  name: 'Buttons',
  data() {
    return {
      btContent:'反馈'
    };
  },
  methods:{
    show(){
      alert('tanchuang')
    }
  },
  components: {
    MsButton
  }
};
</script>

<style scoped>
/* 样式 */
</style>