<template>
  <div class="lesson">
    <Lesson
      v-for="lesson in db"
      :key="lesson.id"
      :lesson="lesson"
      @del="del"
      v-model.toupper.substr_3="lesson.title"
      v-model:price="lesson.price" />
    {{ db }}
  </div>
</template>

<script>
  import db from "../../data/db.js";
  import Lesson from "../components/Lesson.vue";
  import MsInput from "../components/MsInput.vue";
  export default {
    name: "Lessons",
    components: {
      Lesson,
      MsInput,
    },
    data() {
      return {
        db,
        title: "标题",
      };
    },
    methods: {
      del(id) {
        const index = this.db.findIndex((l) => l.id === id);
        this.db.splice(index, 1);
        console.log(id);
      },
      change(val) {
        this.title = val;
      },
    },
  };
</script>

<style scoped lang="scss">
  .lesson {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 10px;
  }
</style>
