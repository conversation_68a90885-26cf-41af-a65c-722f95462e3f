<template>
  <div class="lesson">
    <Lesson v-for="lesson in db" :key="lesson.id" :lesson="lesson" @del="del" />
  </div>
  <!-- <input type="text" :value="title" @input="title=$event.target.value"/> -->
  {{title}}
  <hr>
  <!-- <MsInput :value="title" @update:value="change"/> -->
  <!-- <MsInput v-model:value="title"/> -->
  <MsInput v-model="title"/>
</template>

<script>
  import db from "../../data/db.js";
  import Lesson from "../components/Lesson.vue";
  import MsInput from "../components/MsInput.vue";
  export default {
    name: "Myclicks",
    components: {
      Lesson,
      MsInput
    },
    data() {
      return {
        db,
        title:'标题'
      };
    },
    methods: {
      del(id) {
        const index=this.db.findIndex(l=>l.id===id)
        this.db.splice(index,1)
        console.log(id)
      },
      change(val){
        this.title=val
      }
    },
  };
</script>

<style scoped lang="scss">
    .lesson{
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        column-gap: 10px;
    }
</style>
