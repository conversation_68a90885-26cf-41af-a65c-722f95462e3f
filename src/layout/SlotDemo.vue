<template>
  <lesson-solt v-for="lesson in lessonsData" :key="lesson.id" :LessonSolt="lesson" #default="{id}">
    <!-- <template #default="{id}"> -->
        <button @click="del(id)">删除</button>
    <!-- </template> -->
    </lesson-solt>
</template>

<script>
import lessonsData from '../../data/db';
import LessonSolt from '../components/LessonSolt.vue';
export default {
  name: 'slot-demo',
  data() {
    return {
        lessonsData
    };
  },
  components: {
    LessonSolt,
  },
  methods:{
    del(id){
        console.log(id);
      const index=  this.lessonsData.findIndex(i=>i.id==id)
      this.lessonsData.splice(index,1)
    //   this.lessonsData = this.lessonsData.filter(item=>item.id !== lesson.id)
    }
  }
};
</script>

<style scoped>
/* 样式 */
</style>