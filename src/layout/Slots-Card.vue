<template>
  <div>
    <Card>
      <template #header>顶部</template>
        <div>内容</div>
        <template #footer>底部</template>
    </Card>
  </div>
</template>

<script>
import Card from './Card.vue';
import msButton from '../components/Buttons.vue'
export default {
  name: 'slots', 
  components: {
    Card,
    msButton
  },
  methods: {
    show(content) {
      console.log('插槽',content);
    }
  },

  data() {
    return {
      content:'父组件的内容'
    };
  }
};
</script>

<style scoped>
/* 样式 */
</style>