<template>
  <div>
    <!-- <h2>迷失</h2>
    <div style="color: red" @click="show(content)">
      <slot />
    </div> -->
    <header>
        <slot name="header">
        </slot>
    </header>
    <main>
        <slot>
        </slot>
    </main>
    <footer>
        <slot name="footer">
        </slot>
    </footer>
  </div>
</template>

<script>
  export default {
    name: "card",
    props: ["content"],
    data() {
      return {};
    },
    methods: {
      show(content) {
        console.log("子组件", content);
      },
    },
  };
</script>

<style scoped lang="scss">
  div {
    border: 1px solid #ddd;
    header,
    main,
    footer {
      padding: 10px;
    }
    main {
      border-width: 1px 0 1px 0;
      border-style: solid;
      border-color: #ddd;
    }
  }
</style>
